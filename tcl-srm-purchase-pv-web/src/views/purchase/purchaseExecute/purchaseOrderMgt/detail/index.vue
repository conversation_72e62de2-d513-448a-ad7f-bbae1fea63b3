<template>
  <div class="postpone-full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div>
              <!-- <span>{{ dataForm.rfxCode }}</span> -->
              <span class="sub-title">{{
                `${$t('状态')}: ${getStatusText(dataForm.orderStatus) || ''}`
              }}</span>
              <span class="sub-title">{{
                `${$t('采购订单号')}: ${dataForm.orderCode || ''}`
              }}</span>
              <span class="sub-title">{{
                `${$t('创建人')}: ${dataForm.createUserName || ''}`
              }}</span>
              <span class="sub-title">{{ `${$t('创建时间')}: ${dataForm.createTime || ''}` }}</span>
              <span class="sub-title">{{
                `${$t('最后修改人')}: ${dataForm.updateUserName || ''}`
              }}</span>
              <span class="sub-title">{{
                `${$t('最后修改时间')}: ${dataForm.updateTime || ''}`
              }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
              <vxe-select
                v-model="dataForm.businessType"
                :options="businessTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                filterable
                :disabled="isSup || !editable"
                :placeholder="$t('请选择业务类型')"
              />
            </mt-form-item>
            <mt-form-item prop="contractCode" :label="$t('合同编号')" label-style="top">
              <vxe-input
                v-model="dataForm.contractCode"
                :disabled="isSup || !editable"
                readonly
                :placeholder="$t('请输入合同编号')"
                suffix-icon="vxe-icon-search"
                @suffix-click="choseContractCode"
              />
            </mt-form-item>
            <mt-form-item prop="contractName" :label="$t('合同名称')" label-style="top">
              <vxe-input
                v-model="dataForm.contractName"
                clearable
                disabled
                :placeholder="$t('请输入合同名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-select
                v-model="dataForm.companyCode"
                :options="companyList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                clearable
                filterable
                :disabled="isSup || editableType === 2 || !editable"
                :placeholder="$t('请选择公司')"
                @change="(e) => handleValueChange('company', e)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')">
              <vxe-select
                v-model="dataForm.purchaseOrgCode"
                :options="purchaseOrgList"
                :option-props="{ label: 'text', value: 'organizationCode' }"
                clearable
                filterable
                :disabled="isSup || !dataForm.companyCode || !editable"
                :placeholder="$t('请选择采购组织')"
                @change="(e) => handleValueChange('purchaseOrg', e)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseUserCode" :label="$t('采购员')" label-style="top">
              <vxe-pulldown ref="pulldownRef" destroy-on-close>
                <template #default>
                  <vxe-input
                    :value="
                      dataForm.purchaseUserCode
                        ? `${dataForm.purchaseUserCode}-${dataForm.purchaseUserName}`
                        : ''
                    "
                    suffix-icon="vxe-icon-caret-down"
                    readonly
                    :disabled="isSup || !editable"
                    :placeholder="$t('请选择采购员')"
                    @click="handlePulldown"
                  />
                </template>
                <template #dropdown>
                  <vxe-input
                    ref="purchaserSearchInputRef"
                    v-model="purchaserSearchValue"
                    :prefix-icon="'vxe-icon-search'"
                    clearable
                    :placeholder="$t('搜索')"
                    @input="handlePulldownSearchInput"
                  />
                  <vxe-list class="my-dropdown2" :data="purchaserList" auto-resize height="auto">
                    <template #default="{ items }">
                      <div v-if="items.length">
                        <div
                          class="list-item2"
                          v-for="item in items"
                          :key="item.employeeId"
                          @click="handlePulldownItemSelected(item)"
                        >
                          <span
                            :class="{
                              isSelected: item.employeeCode === dataForm.purchaserUserCode
                            }"
                            >{{ item.text }}</span
                          >
                        </div>
                      </div>
                      <div v-else class="empty-tip">
                        {{ $t('暂无数据') }}
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <vxe-pulldown ref="pulldownSupplierRef" destroy-on-close>
                <template #default>
                  <vxe-input
                    :value="
                      dataForm.supplierCode
                        ? `${dataForm.supplierCode}-${dataForm.supplierName}`
                        : ''
                    "
                    suffix-icon="vxe-icon-caret-down"
                    readonly
                    :disabled="isSup || editableType === 2 || !editable"
                    :placeholder="$t('请选择供应商')"
                    @click="handlePulldownSupplier"
                  />
                </template>
                <template #dropdown>
                  <vxe-input
                    ref="supplierSearchInputRef"
                    v-model="supplierSearchValue"
                    :prefix-icon="'vxe-icon-search'"
                    clearable
                    :placeholder="$t('搜索')"
                    @input="handlePulldownSearchInputSupplier"
                  />
                  <vxe-list class="my-dropdown2" :data="supplierList" auto-resize height="auto">
                    <template #default="{ items }">
                      <div v-if="items.length">
                        <div
                          class="list-item2"
                          v-for="item in items"
                          :key="item.supplierCode"
                          @click="handlePulldownItemSelectedSupplier(item)"
                        >
                          <span
                            :class="{
                              isSelected: item.supplierCode === dataForm.supplierCode
                            }"
                            >{{ item.text }}</span
                          >
                        </div>
                      </div>
                      <div v-else class="empty-tip">
                        {{ $t('暂无数据') }}
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
            <!-- <mt-form-item prop="warehouseCode" :label="$t('入库仓库')" label-style="top">
              <vxe-select
                v-model="dataForm.warehouseCode"
                :options="warehouseList"
                :option-props="{ label: 'text', value: 'warehouseCode' }"
                clearable
                filterable
                :disabled="isSup || editableType === 2 || !editable"
                :placeholder="$t('请选择入库仓库')"
                @change="(e) => handleValueChange('warehouse', e)"
              />
            </mt-form-item>
            <mt-form-item prop="consignee" :label="$t('收货人')" label-style="top">
              <vxe-input
                v-model="dataForm.consignee"
                :disabled="isSup || !editable"
                :placeholder="$t('请输入收货人')"
              />
            </mt-form-item>
            <mt-form-item prop="contactPhone" :label="$t('收货人联系方式')" label-style="top">
              <vxe-input
                v-model="dataForm.contactPhone"
                :disabled="isSup || !editable"
                :placeholder="$t('请输入收货人联系方式')"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryAddress" :label="$t('收货地址')" label-style="top">
              <vxe-input
                v-model="dataForm.deliveryAddress"
                :disabled="isSup || !editable"
                :placeholder="$t('请输入收货地址')"
              />
            </mt-form-item> -->
            <mt-form-item prop="deliveryDriverType" :label="$t('发货驱动类型')" label-style="top">
              <vxe-select
                v-model="dataForm.deliveryDriverType"
                :options="deliveryDriverTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                :disabled="isSup || !editable"
                filterable
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
              <vxe-select
                v-model="dataForm.currencyCode"
                :options="currencyList"
                :option-props="{ label: 'text', value: 'currencyCode' }"
                clearable
                filterable
                :disabled="isSup || !editable"
                :placeholder="$t('请选择币种')"
                @change="(e) => handleValueChange('currency', e)"
              />
            </mt-form-item>
            <mt-form-item prop="tradeMethod" :label="$t('贸易方式')" label-style="top">
              <vxe-input
                v-model="dataForm.tradeMethod"
                clearable
                :disabled="isSup || !editable"
                :placeholder="$t('请输入贸易方式')"
              />
            </mt-form-item>
            <mt-form-item prop="paymentType" :label="$t('付款方式')" label-style="top">
              <vxe-select
                v-model="dataForm.paymentType"
                :options="paymentTypeList"
                :option-props="{ label: 'dictName', value: 'dictCode' }"
                clearable
                filterable
                :disabled="isSup || !editable"
                :placeholder="$t('请选择')"
                @change="(e) => handleValueChange('paymentType', e)"
              />
            </mt-form-item>
            <mt-form-item prop="paymentTerms" :label="$t('付款条件')" label-style="top">
              <vxe-select
                v-model="dataForm.paymentTerms"
                :options="paymentTermsList"
                :option-props="{ label: 'dictName', value: 'dictCode' }"
                clearable
                filterable
                :disabled="isSup || !editable"
                :placeholder="$t('请选择')"
                @change="(e) => handleValueChange('paymentTerms', e)"
              />
            </mt-form-item>
            <mt-form-item prop="orderSource" :label="$t('订单来源')" label-style="top">
              <vxe-select
                v-model="dataForm.orderSource"
                :options="dataSourceList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                filterable
                disabled
                :placeholder="$t('请选择')"
              />
            </mt-form-item>

            <mt-form-item prop="purchaseRemark" :label="$t('采方备注')" label-style="top">
              <vxe-textarea
                v-model="dataForm.purchaseRemark"
                clearable
                :disabled="isSup || !editable"
                :rows="1"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>

            <mt-form-item prop="supplierRemark" :label="$t('供方备注')" label-style="top">
              <vxe-textarea
                v-model="dataForm.supplierRemark"
                clearable
                :disabled="!isSup || $route.query.type === 'detail'"
                :rows="1"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container" v-show="$route.query.type !== 'add'">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :data-info="dataForm"
          :editable-type="editableType"
          :editable="editable"
          :is-sup="isSup"
          :factory-list="factoryList"
          :detail-info="dataForm"
          @updateDetail="init"
          @updateItemList="updateItemList"
          @itemDataChange="itemDataChange"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea,
  Pulldown as VxePulldown
} from 'vxe-table'
import mixin from './../config/mixin'
import utils from '@/utils/utils'
import {
  businessTypeList,
  deliveryDriverTypeList,
  orderStatusList,
  syncOaStatusList,
  dataSourceList
} from './../config/index'
import debounce from 'lodash.debounce'
import dayjs from 'dayjs'

export default {
  name: 'ExpertRating',
  components: {
    VxeInput,
    VxeButton,
    VxeSelect,
    VxeTextarea,
    VxePulldown
  },
  mixins: [mixin],
  data() {
    return {
      businessTypeList,
      deliveryDriverTypeList,
      orderStatusList,
      syncOaStatusList,
      dataSourceList,
      currencyList: [],
      supplierList: [],
      itemDetailAddDtoList: [],
      fileRequestList: [],
      warehouseList: [],
      dataForm: {
        purchaseUserCode: JSON.parse(sessionStorage.getItem('userInfo'))?.employeeCode || null,
        purchaseUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.accountName || null,
        orderSource: 0,
        orderStatus: 0
      },
      paymentTypeList: utils.getPurchasePv('PaymentType'),
      paymentTermsList: utils.getPurchasePv('payMethod'),
      activeTabIndex: 0,
      isExpand: true,
      type: 'detail',
      isInit: true,
      purchaserSearchValue: null,
      supplierSearchValue: null,
      keepArr: ['ItemTab', 'PurchaseOrderTab', 'AttachmentTab'],
      editableType: 0,
      // 默认为0均可编辑
      // 判断是否可编辑的三个类型
      // 1）订单来源=手工创建，只要所有明细行没有在途及入库，审批通过及供应商已接受的采购订单都能修改，能修改的信息与草稿状态一致，可以换抬头供应商、公司主题、物料明细物料、删除物料行等，若明细行存在在途或已入库，则那行不允许删除行、更改物料、价格等，可以改订单数量，控制订单数量不能修改成小于已发货的数量，更改后自动更新状态为已发布，发布供应商端；
      // 2）订单来源=采购申请，草稿、审批通过及供应商确认的采购订单，不能换供应商、公司主体，不能删除/新增物料明细，不能变更物料，只能修改价格、税率、订单数量、要求交期和备注，若已存在发货数量，控制订单数量不能修改成小于已发货的数量，也不能修改价格，修改采购订单后，单据状态更新为已发布，需供应商确认，确认后自动提交OA审批；
      // 3）订单来源=合同转化，草稿、审批通过及供应商确认的采购订单，不能换供应商、公司主体，可以删除物料行，不能新增物料行，不能变更物料，只能修改订单数量、要求交期和备注，若已存在发货数量，控制订单数量不能修改成小于已发货的数量，也不能大于合同明细中的数量（若原先转的合同上有标明数量），也不能修改价格，修改采购订单后，若原先转的合同上有标明数量，单据状态更新为无需审批+供应商已接受，若原先转的合同上无数量，需供应商确认，确认后自动提交OA审批
      isSup: false
    }
  },
  computed: {
    editable() {
      // 订单状态是草稿、供应商已拒绝、采购已拒绝、双方已确认，待采购确认且未审批，采购方可以编辑订单
      return (
        this.$route.query.type !== 'detail' &&
        ([0, 2, 4, 5].includes(this.dataForm.orderStatus) ||
          (this.dataForm.orderStatus === 3 && this.dataForm.approvalStatus === 0)) &&
        !this.isSup
      )
    },
    tabList() {
      const tabs = [
        { title: this.$t('物料明细'), compName: 'ItemTab' },
        { title: this.$t('附件'), compName: 'AttachmentTab' }
      ]
      return tabs
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 物料明细
          comp = () => import('./components/itemTab.vue')
          break
        case 1:
          // 附件
          comp = () => import('./components/attachment.vue')
          break

        default:
          return
      }
      return comp
    },
    formRules() {
      return {
        orderCode: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        contractCode: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        businessType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        purchaseOrgCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        contractType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        currencyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        warehouseCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        purchaseUserCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        deliveryDriverType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orderSource: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    },
    detailToolbar() {
      return [
        {
          code: 'back',
          name: this.$t('返回')
        },
        // {
        //   code: 'viewOA',
        //   name: this.$t('OA审批进度'),
        //   status: '',
        //   isHidden: !this.dataForm?.oaApproveLink
        // },
        {
          code: 'save',
          name: this.$t('保存'),
          isHidden: this.isSup || !this.editable,
          status: 'primary'
        },
        {
          code: 'publish',
          name: this.$t('发布'),
          isHidden: this.isSup || !this.editable,
          status: 'primary'
        },
        {
          code: 'submit',
          name: this.$t('提交'),
          // 支持供方待供应商确认才能提交
          isHidden:
            !this.isSup || this.dataForm.orderStatus !== 1 || this.$route.query.type === 'detail',
          status: 'primary'
        }
      ]
    }
  },
  mounted() {
    // this.getWarehouseList()
    if (this.$route.path?.includes('-sup')) {
      this.isSup = true
    }
    this.init()
  },
  methods: {
    // 获取仓库下拉列表
    async getWarehouseList() {
      const params = {
        page: {
          current: 1,
          size: 1000
        }
      }
      const res = await this.$API.purchaseOrderMgt.queryPvWarehouseList(params)
      if (res.code === 200) {
        res.data.records.forEach((item) => {
          item.text = item.warehouseCode + '-' + item.warehouseName
        })
        this.warehouseList = res.data.records
      }
    },
    choseContractCode() {
      if (this.isSup || !this.editable) {
        return
      }
      this.$dialog({
        modal: () => import('./components/contractList'),
        data: {
          title: this.$t('选择合同')
        },
        success: (selectedRecords) => {
          if (selectedRecords && selectedRecords.length > 0) {
            // 获取第一个选中记录作为主记录，用于设置其他字段
            const firstRecord = selectedRecords[0]

            // 用英文逗号拼接合同编号和名称
            this.dataForm.contractCode = selectedRecords.map((item) => item.contractCode).join(',')
            this.dataForm.contractName = selectedRecords.map((item) => item.contractName).join(',')

            // 设置其他字段（使用第一个选中记录的值）
            this.dataForm.companyCode = firstRecord.companyCode
            this.dataForm.companyName = firstRecord.companyName
            const selectedItem = this.companyList.find(
              (item) => item.orgCode === firstRecord.companyCode
            )
            this.dataForm.companyId = selectedItem ? selectedItem.id : null
            this.getPurchaseOrgList(selectedItem?.id)
            this.dataForm.purchaseOrgCode = firstRecord.purchaseOrgCode
            this.dataForm.purchaseOrgName = firstRecord.purchaseOrgName
            this.dataForm.supplierCode = firstRecord.supplierCode
            this.dataForm.supplierName = firstRecord.supplierName
            this.dataForm.currencyCode = firstRecord.currencyCode
            this.dataForm.currencyName = firstRecord.currencyName
            this.dataForm.businessType = firstRecord.businessType
          }
        }
      })
    },
    // 远程搜索查询-展开面板
    handlePulldown() {
      this.$refs.pulldownRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.purchaserSearchInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInput: debounce(function (e) {
      this.getPurchaserList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelected(item) {
      if (this.$refs.pulldownRef) {
        this.$set(this.dataForm, 'purchaseUserCode', item.employeeCode)
        this.$set(this.dataForm, 'purchaseUserName', item.employeeName)
        this.$refs.pulldownRef.hidePanel()
        this.purchaserSearchValue = null
      }
    },
    // 远程搜索查询-展开面板
    handlePulldownSupplier() {
      this.$refs.pulldownSupplierRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.supplierSearchInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInputSupplier: debounce(function (e) {
      this.getSupplierList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelectedSupplier(item) {
      if (this.$refs.pulldownSupplierRef) {
        this.$set(this.dataForm, 'supplierCode', item.supplierCode)
        this.$set(this.dataForm, 'supplierName', item.supplierName)
        this.$refs.pulldownSupplierRef.hidePanel()
        this.supplierSearchValue = null
      }
    },
    getStatusText() {
      const item = orderStatusList.find((item) => item.value === this.dataForm.orderStatus)
      return item?.text || ''
    },
    // 初始化
    init() {
      if (this.$route.query.type !== 'add') {
        if (this.$route.query.orderSource && Number(this.$route.query.orderSource) === 2) {
          // 合同转订单
          this.getHeaderInfo(2)
        } else {
          this.getHeaderInfo()
        }
      } else {
        this.getPurchaserList()
        // 设置默认值
        this.dataForm = {
          purchaseUserCode: JSON.parse(sessionStorage.getItem('userInfo'))?.employeeCode || null,
          purchaseUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.accountName || null,
          orderSource: 0,
          orderStatus: 0,
          contractCode: null,
          contractName: null,
          companyId: null,
          companyCode: null,
          companyName: null,
          purchaseOrgCode: null,
          purchaseOrgName: null,
          supplierCode: null,
          supplierName: null,
          currencyCode: null,
          currencyName: null
        }
      }
      // 获取币种
      this.getCurrencyList()
      // 获取供应商
      this.getSupplierList()
    },
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      this.currencyList = res.data.map((i) => {
        return {
          text: i.currencyCode + '-' + i.currencyName,
          ...i
        }
      })
    },
    async getSupplierList(searchText) {
      const params = {
        page: {
          size: 20,
          current: 1
        }
      }
      // if (searchText && isNaN(searchText)) {
      //   params.supplierName = searchText
      // } else {
      //   params.supplierCode = searchText
      // }
      // const res = await this.$API.masterData.getSupplierList(params)
      if (searchText) {
        params.condition = 'or'
        params.rules = [
          {
            field: 'supplierName',
            label: '',
            operator: 'contains',
            type: 'string',
            value: searchText
          },
          {
            field: 'supplierCode',
            label: '',
            operator: 'contains',
            type: 'string',
            value: searchText
          }
        ]
      }
      const res = await this.$API.masterData.getStandardSupplierInfoList(params)
      this.supplierList = res.data?.records?.map((i) => {
        return {
          text: i.supplierCode + '-' + i.supplierName,
          ...i
        }
      })
    },

    // 监听定价明细数据
    itemDataChange(data) {
      switch (this.activeTabIndex) {
        case 0:
          this.itemDetailAddDtoList = data
          break
        case 1:
          this.pvFileRequestList = data
          break
        default:
          break
      }
    },
    // 获取头部基础信息
    async getHeaderInfo(orderSource) {
      let res = {}
      let listRes = {}
      let fileListRes = {}
      if (orderSource === 2) {
        // 合同转订单获取详情
        res = await this.$API.contract.contractTransferOrder({
          ids: Array.isArray(this.$route.query.ids)
            ? this.$route.query.ids
            : [this.$route.query.ids]
        })
        if (res.code === 200) {
          res.data.orderStatus = 0
          listRes = {
            data: res.data.detailList
          }
        }
      } else {
        // 默认手工新建 || 列表页跳转进来的场景获取订单详情
        res = await this.$API.purchaseOrderMgt.getDetailHeader({
          id: this.$route.query.id
        })
        listRes = await this.$API.purchaseOrderMgt.getDetailList({
          id: this.$route.query.id
        })
        fileListRes = await this.$API.purchaseOrderMgt.getFileList({
          id: this.$route.query.id
        })
      }
      if (res.code === 200) {
        this.dataForm = { ...res.data }
        // if (listRes.data.length) {
        //   this.dataForm = {
        //     ...this.dataForm,
        //     warehouseCode: listRes.data[0].warehouseCode,
        //     warehouseName: listRes.data[0].warehouseName,
        //     consignee: listRes.data[0].consignee,
        //     contactPhone: listRes.data[0].contactPhone,
        //     deliveryAddress: listRes.data[0].deliveryAddress
        //   }
        // }
        this.$set(this.dataForm, 'detailList', listRes.data)
        this.$set(this.dataForm, 'fileList', fileListRes.data)
        listRes.data.forEach((i) => {
          if (
            this.dataForm.orderSource === 0 &&
            (i.transitQty ||
              i.warehouseQty ||
              i.approvalStatus === 2 ||
              i.supplierConfirmStatus === 1)
          ) {
            // 1）订单来源=手工创建，只要所有明细行没有在途及入库，审批通过及供应商已接受的采购订单都能修改，能修改的信息与草稿状态一致，可以换抬头供应商、公司主题、物料明细物料、删除物料行等，若明细行存在在途或已入库，则那行不允许删除行、更改物料、价格等，可以改订单数量，控制订单数量不能修改成小于已发货的数量，更改后自动更新状态为已发布，发布供应商端；
            this.editableType = 1
          }
          if (
            this.dataForm.orderSource === 3 &&
            (i.orderStatus === 0 || i.approvalStatus === 2 || i.supplierConfirmStatus === 1)
          ) {
            // 2）订单来源=采购申请，草稿、审批通过及供应商确认的采购订单，不能换供应商、公司主体，不能删除/新增物料明细，不能变更物料，只能修改价格、税率、订单数量、要求交期和备注，若已存在发货数量，控制订单数量不能修改成小于已发货的数量，也不能修改价格，修改采购订单后，单据状态更新为已发布，需供应商确认，确认后自动提交OA审批
            this.editableType = 2
          }
          if (
            this.dataForm.orderSource === 2 &&
            (i.orderStatus === 0 || i.approvalStatus === 2 || i.supplierConfirmStatus === 1)
          ) {
            // 3）订单来源=合同转化，草稿、审批通过及供应商确认的采购订单，不能换供应商、公司主体，可以删除物料行，不能新增物料行，不能变更物料，只能修改订单数量、要求交期和备注，若已存在发货数量，控制订单数量不能修改成小于已发货的数量，也不能大于合同明细中的数量（若原先转的合同上有标明数量），也不能修改价格，修改采购订单后，若原先转的合同上有标明数量，单据状态更新为无需审批+供应商已接受，若原先转的合同上无数量，需供应商确认，确认后自动提交OA审批
            this.editableType = 3
          }
          // 承诺日期和承诺数量需要默认等于要求交期和订单数量
          if (!i.promiseQty) {
            i.promiseQty = i.quantity
          }
          if (!i.promiseTime) {
            i.promiseTime = i.requiredDeliveryDate
          }
        })
        this.itemDetailAddDtoList = this.dataForm.detailList
        this.pvFileRequestList = this.dataForm.fileList
        this.getCompanyList(true)
        this.getPurchaserList(res.data?.purchaseUserName)
        this.getSupplierList(res.data?.supplierCode)
      }
    },

    async updateItemList() {
      const listRes = await this.$API.purchaseOrderMgt.getDetailList({
        id: this.$route.query.id
      })
      this.$set(this.dataForm, 'detailList', listRes.data)
      this.itemDetailAddDtoList = this.dataForm.detailList
    },

    // 下拉列表选中值修改
    handleValueChange(prefix, e) {
      const { value } = e
      switch (prefix) {
        // 选择公司
        case 'company':
          this.$set(this.dataForm, 'purchaseOrgCode', null)
          // this.$set(this.dataForm, 'factoryCode', null)

          if (value) {
            const selectedItem = this.companyList.find((item) => item.orgCode === value)
            this.dataForm.companyId = selectedItem ? selectedItem.id : null
            this.dataForm.companyName = selectedItem ? selectedItem.orgName : null

            this.getPurchaseOrgList(selectedItem?.id)
          } else {
            this.dataForm.companyId = null
            this.dataForm.companyName = null
          }
          break
        // 选择采购组织
        case 'purchaseOrg':
          // this.$set(this.dataForm, 'factoryCode', null)
          if (value) {
            const selectedItem = this.purchaseOrgList.find(
              (item) => item.organizationCode === value
            )
            this.dataForm.purchaseOrgId = selectedItem ? selectedItem.id : null
            this.dataForm.purchaseOrgName = selectedItem ? selectedItem.organizationName : null

            const { companyId, purchaseOrgId } = this.dataForm
            this.getFactoryListByCompanyAndPur(companyId, purchaseOrgId)
          } else {
            this.dataForm.purchaseOrgId = null
            this.dataForm.purchaseOrgName = null

            // this.dataForm.factoryCode = null
            // this.dataForm.factoryName = null
          }
          break
        // 选择币种
        case 'currency':
          if (value) {
            const selectedItem = this.currencyList.find((item) => item.currencyCode === value)
            this.dataForm.currencyName = selectedItem ? selectedItem.currencyName : null
          } else {
            this.dataForm.currencyName = null
          }
          break
        // 选择采购负责人
        case 'purchaseUser':
          if (e) {
            this.dataForm.purchaseUserName = e ? e.employeeName : null
          } else {
            this.dataForm.purchaseUserName = null
          }
          break
        // 选择供应商
        case 'supplier':
          if (value) {
            const selectedItem = this.supplierList.find((item) => item.supplierCode === value)
            this.dataForm.supplierName = selectedItem ? selectedItem.supplierName : null
          } else {
            this.dataForm.supplierName = null
          }
          break
        // 入库仓库
        case 'warehouse':
          if (value) {
            const selectedItem = this.warehouseList.find((item) => item.warehouseCode === value)
            this.dataForm.warehouseName = selectedItem ? selectedItem.warehouseName : null
          } else {
            this.dataForm.warehouseName = null
          }
          break
        // 付款方式
        case 'paymentType':
          if (value) {
            const selectedItem = this.paymentTypeList.find((item) => item.dictCode === value)
            this.dataForm.paymentTypeName = selectedItem ? selectedItem.dictName : null
          } else {
            this.dataForm.paymentTypeName = null
          }
          break
        // 付款条件
        case 'paymentTerms':
          if (value) {
            const selectedItem = this.paymentTermsList.find((item) => item.dictCode === value)
            this.dataForm.paymentTermsName = selectedItem ? selectedItem.dictName : null
          } else {
            this.dataForm.paymentTermsName = null
          }
          break
        // 选择工厂
        // case 'factory':
        //   if (value) {
        //     const selectedItem = this.factoryList.find((item) => item.orgCode === value)
        //     this.dataForm.factoryName = selectedItem ? selectedItem.orgName : null
        //   } else {
        //     this.dataForm.factoryName = null
        //   }
        //   break
        default:
          break
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'publish':
          this.handlePublish(e.code)
          break
        case 'submit':
          this.handleSubmit()
          break
        case 'viewOA':
          window.open(this.dataForm?.oaApproveLink)
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 处理提交数据
    getSubmitData() {
      let params = null
      this.$refs.dataFormRef.validate(async (valid) => {
        console.log('valid', valid)
        if (!valid) {
          //展开头部表单数据，显示报错信息
          this.isExpand = true
          return
        }
        params = {
          ...this.dataForm,
          orderDate: this.dataForm.orderDate ? this.getUnix(dayjs(this.dataForm.orderDate)) : null,
          detailList:
            this.itemDetailAddDtoList.map((i) => {
              return {
                ...i,
                requiredDeliveryDate: i.requiredDeliveryDate
                  ? this.getUnix(dayjs(i.requiredDeliveryDate))
                  : null,
                urgentDate: i.urgentDate ? this.getUnix(dayjs(i.urgentDate)) : null,
                orderDate: i.orderDate ? this.getUnix(dayjs(i.orderDate)) : null,
                promiseTime: i.promiseTime ? this.getUnix(dayjs(i.promiseTime)) : null,
                id: i.id.includes('row_') || !i.id ? null : i.id
              }
            }) || [],
          fileList: this.pvFileRequestList
        }
      })
      return params
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    // 处理保存数据----不需要校验必填
    getSaveData() {
      let params = {
        ...this.dataForm,
        signDate: this.dataForm.signDate ? this.getUnix(dayjs(this.dataForm.signDate)) : null,
        orderDate: this.dataForm.orderDate ? this.getUnix(dayjs(this.dataForm.orderDate)) : null,
        urgentDate: this.dataForm.urgentDate ? this.getUnix(dayjs(this.dataForm.urgentDate)) : null,
        promiseTime: this.dataForm.promiseTime
          ? this.getUnix(dayjs(this.dataForm.promiseTime))
          : null,
        detailList:
          this.itemDetailAddDtoList.map((i) => {
            return {
              ...i,
              requiredDeliveryDate: i.requiredDeliveryDate
                ? this.getUnix(dayjs(i.requiredDeliveryDate))
                : null,
              urgentDate: i.urgentDate ? this.getUnix(dayjs(i.urgentDate)) : null,
              orderDate: i.orderDate ? this.getUnix(dayjs(i.orderDate)) : null,
              promiseTime: i.promiseTime ? this.getUnix(dayjs(i.promiseTime)) : null,
              id: i.id.includes('row_') || !i.id ? null : i.id
            }
          }) || [],
        fileList: this.pvFileRequestList || []
      }
      return params
    },
    // 保存
    async handleSave(type) {
      const params = this.getSaveData()
      if (Object.entries(params).length === 0) return
      this.$store.commit('startLoading')
      const res = await this.$API.purchaseOrderMgt.saveDetail(params)
      this.$store.commit('endLoading')
      if (res.code === 200 && type !== 'publish') {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        let routerName = 'purchase-coordination-pv-detail'
        if (this.isSup) {
          routerName = 'purchase-coordination-pv-detail-sup'
        }
        this.$router.replace({
          name: routerName,
          query: {
            type: 'edit',
            id: res.data,
            timeStamp: new Date().getTime()
          }
        })
      }
    },

    // 发布
    async handlePublish(type) {
      const params = this.getSubmitData(type)
      if (!params) {
        this.$toast({ content: this.$t('请完成必填项后进行操作'), type: 'warning' })
        return
      }
      for (let i = 0; i < params.detailList.length; i++) {
        const item = params.detailList[i]
        if (!item.siteCode) {
          this.$toast({ content: this.$t(`物料明细第${i + 1}行请选择工厂`), type: 'warning' })
          return
        }
        if (!item.itemCode) {
          this.$toast({ content: this.$t(`物料明细第${i + 1}行请选择物料`), type: 'warning' })
          return
        }
        if (!item.quantity) {
          this.$toast({ content: this.$t(`物料明细第${i + 1}行请输入订单数量`), type: 'warning' })
          return
        }
        if (!item.taxCode) {
          this.$toast({ content: this.$t(`物料明细第${i + 1}行请选择税率`), type: 'warning' })
          return
        }
        if (!item.taxedPrice) {
          this.$toast({ content: this.$t(`物料明细第${i + 1}行请输入含税单价`), type: 'warning' })
          return
        }
        if (!item.requiredDeliveryDate) {
          this.$toast({ content: this.$t(`物料明细第${i + 1}行请选择要求交期`), type: 'warning' })
          return
        }
        if (!item.baseUnitCode) {
          this.$toast({ content: this.$t(`物料明细第${i + 1}行请选择基本单位`), type: 'warning' })
          return
        }
        if (this.dataForm.deliveryDriverType === 1 && !item.warehouseCode) {
          this.$toast({ content: this.$t(`物料明细第${i + 1}行请选择入库仓库`), type: 'warning' })
          return
        }
      }
      this.$store.commit('startLoading')
      this.handleSave('publish')
      const res = await this.$API.purchaseOrderMgt.publishDetail(params)
      this.$store.commit('endLoading')
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.getHeaderInfo()
        if (this.activeComponent?.name !== 'AttachmentTab') {
          this.$refs.mainContent?.initTableData()
        }
      }
    },
    // 提交反馈
    async handleSubmit() {
      // 校验供方反馈备注必填条件
      for (let i = 0; i < this.itemDetailAddDtoList.length; i++) {
        const item = this.itemDetailAddDtoList[i]
        const promiseDate = item.promiseTime ? dayjs(item.promiseTime).format('YYYY-MM-DD') : ''
        const requiredDate = item.requiredDeliveryDate
          ? dayjs(item.requiredDeliveryDate).format('YYYY-MM-DD')
          : ''
        if (
          (promiseDate !== requiredDate || item.promiseQty !== item.quantity) &&
          !item.supplierRemark
        ) {
          this.$toast({
            content: this.$t(
              `物料明细第${i + 1}行承诺日期或承诺数量与订单不一致时,供方反馈备注必填`
            ),
            type: 'warning'
          })
          return
        }
      }
      const params = {
        id: this.dataForm.id, //主键ID
        detailList:
          this.itemDetailAddDtoList.map((i) => {
            return {
              promiseTime: i.promiseTime ? this.getUnix(dayjs(i.promiseTime)) : null,
              orderDate: i.orderDate ? this.getUnix(dayjs(i.orderDate)) : null,
              promiseQty: i.promiseQty,
              supplierRemark: i.supplierRemark,
              // id: i.id ? (i.id.includes('row_') ? null : i.id) : null
              id: i.id.includes('row_') || !i.id ? null : i.id
            }
          }) || [],
        supplierRemark: this.dataForm.supplierRemark || null
      }
      this.$store.commit('startLoading')
      const res = await this.$API.purchaseOrderMgt.feedbackDetail(params)
      this.$store.commit('endLoading')
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.getHeaderInfo()
        if (this.activeComponent?.name !== 'AttachmentTab') {
          this.$refs.mainContent?.initTableData()
        }
      }
    },
    // 弹框展示
    async showDialog(message, cssClass) {
      return new Promise((resolve) => {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(message),
            cssClass
          },
          success: resolve
        })
      })
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.postpone-full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.body-container {
  height: calc(100% - 80px);
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      padding-bottom: 8px;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        padding-right: 12px;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
        white-space: nowrap;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>

<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="orderCode" :label="$t('采购订单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.orderCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="orderDate" :label="$t('订单日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.orderDate"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'orderDate')"
          />
        </mt-form-item>
        <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.businessType"
            css-class="rule-element"
            :data-source="businessTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="contractCode" :label="$t('合同编号')" label-style="top">
          <mt-input
            v-model="searchFormModel.contractCode"
            :show-clear-button="true"
            :placeholder="$t('请输入合同编号')"
          />
        </mt-form-item>
        <mt-form-item prop="contractName" :label="$t('合同名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.contractName"
            :show-clear-button="true"
            :placeholder="$t('请输入合同名称')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :show-clear-button="true"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryDriverType" :label="$t('发货驱动类型')" label-style="top">
          <mt-select
            v-model="searchFormModel.deliveryDriverType"
            css-class="rule-element"
            :data-source="deliveryDriverTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="sealSignFlag" :label="$t('是否用印签署')" label-style="top">
          <mt-select
            v-model="searchFormModel.sealSignFlag"
            css-class="rule-element"
            :data-source="sealSignFlagList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseUserCode" :label="$t('采购员编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseUserCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseUserName" :label="$t('采购员名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="orderStatus" :label="$t('订单状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.orderStatus"
            css-class="rule-element"
            :data-source="isSup ? orderStatusSupList : orderStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="publishStatus" :label="$t('发布状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.publishStatus"
            css-class="rule-element"
            :data-source="publishStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="approvalStatus" :label="$t('审批状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.approvalStatus"
            css-class="rule-element"
            :data-source="approvalStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="signStatus" :label="$t('签署状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.signStatus"
            css-class="rule-element"
            :data-source="signStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="urgentStatus" :label="$t('加急状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.urgentStatus"
            css-class="rule-element"
            :data-source="urgentStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="urgentDate" :label="$t('加急日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.urgentDate"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'urgentDate')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryStatus" :label="$t('发货状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.deliveryStatus"
            css-class="rule-element"
            :data-source="deliveryStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="receiveStatus" :label="$t('收货状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.receiveStatus"
            css-class="rule-element"
            :data-source="receiveStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="orderSource" :label="$t('订单来源')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.orderSource"
            css-class="rule-element"
            :data-source="dataSourceList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="currencyName" :label="$t('币种名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.currencyName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="tradeMethod" :label="$t('贸易方式')" label-style="top">
          <mt-input
            v-model="searchFormModel.tradeMethod"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="publishTime" :label="$t('发布时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.publishTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'publishTime')"
          />
        </mt-form-item>
        <mt-form-item prop="feedbackTime" :label="$t('反馈时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.feedbackTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'feedbackTime')"
          />
        </mt-form-item>
        <mt-form-item prop="syncErpStatus" :label="$t('同步极光状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.syncErpStatus"
            css-class="rule-element"
            :data-source="syncOaStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseRemark" :label="$t('采方备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseRemark"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierRemark" :label="$t('供方备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierRemark"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('最后更新时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="scTableRef"
      grid-id="d9548787-74fb-d300-4e68-b62c16ef05d1"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './../config/mixin'
import {
  listToolbar,
  deliveryDriverTypeList,
  orderStatusList,
  orderStatusSupList,
  publishStatusList,
  sealSignFlagList,
  businessTypeList,
  urgentStatusList,
  deliveryStatusList,
  receiveStatusList,
  warehouseStatusList,
  dataSourceList,
  approvalStatusList,
  signStatusList,
  syncOaStatusList
} from './../config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      deliveryDriverTypeList,
      orderStatusList,
      orderStatusSupList,
      publishStatusList,
      sealSignFlagList,
      businessTypeList,
      urgentStatusList,
      deliveryStatusList,
      receiveStatusList,
      warehouseStatusList,
      dataSourceList,
      approvalStatusList,
      signStatusList,
      syncOaStatusList,
      toolbar: [],
      searchFormModel: {},
      tableData: [],
      loading: false,
      type: 'list',
      uploadFileList: [], // 上传的附件
      isSup: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left',
          align: 'center'
        },
        // {
        //   width: 70,
        //   field: 'index',
        //   title: this.$t('序号')
        // },
        {
          field: 'orderCode',
          title: this.$t('采购订单号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{row.orderCode}</a>
              ]
            }
          }
        },
        {
          field: 'orderDate',
          title: this.$t('订单日期'),
          minWidth: 140
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = businessTypeList.find((item) => item.value === row.businessType)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'contractCode',
          title: this.$t('合同编号'),
          minWidth: 180
        },
        {
          field: 'contractName',
          title: this.$t('合同名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.purchaseOrgCode + '-' + row.purchaseOrgName}</span>]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 140
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 140
        },
        {
          field: 'deliveryDriverType',
          title: this.$t('发货驱动类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = deliveryDriverTypeList.find(
                (item) => item.value === row.deliveryDriverType
              )
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'purchaseUserName',
          title: this.$t('采购负责人'),
          minWidth: 150
        },
        {
          field: 'orderStatus',
          title: this.$t('订单状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = orderStatusList.find((item) => item.value === row.orderStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'publishStatus',
          title: this.$t('发布状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = publishStatusList.find((item) => item.value === row.publishStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'sealSignFlag',
          title: this.$t('是否用印签署'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              const selectItem = sealSignFlagList.find((item) => item.value === row.sealSignFlag)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'approvalStatus',
          title: this.$t('审批状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = approvalStatusList.find(
                (item) => item.value === row.approvalStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'signStatus',
          title: this.$t('签署状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = signStatusList.find((item) => item.value === row.signStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'urgentStatus',
          title: this.$t('加急状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = urgentStatusList.find((item) => item.value === row.urgentStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'urgentDate',
          title: this.$t('加急日期'),
          minWidth: 140
        },
        {
          field: 'deliveryStatus',
          title: this.$t('发货状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = deliveryStatusList.find(
                (item) => item.value === row.deliveryStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'receiveStatus',
          title: this.$t('收货状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = receiveStatusList.find((item) => item.value === row.receiveStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'warehouseStatus',
          title: this.$t('入库状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = warehouseStatusList.find(
                (item) => item.value === row.warehouseStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'orderSource',
          title: this.$t('订单来源'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = dataSourceList.find((item) => item.value === row.orderSource)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        // {
        //   field: 'purchaseGroupCode',
        //   title: this.$t('采购组'),
        //   minWidth: 120,
        //   slots: {
        //     default: ({ row }) => {
        //       return [<span>{row.purchaseGroupCode + '-' + row.purchaseGroupName}</span>]
        //     }
        //   }
        // },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.currencyCode + '-' + row.currencyName}</span>]
            }
          }
        },
        {
          field: 'tradeMethod',
          title: this.$t('贸易方式'),
          minWidth: 120
        },
        {
          field: 'paymentType',
          title: this.$t('付款方式'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              return [<span>{row.paymentType + '-' + row.paymentTypeName}</span>]
            }
          }
        },
        {
          field: 'paymentTerms',
          title: this.$t('付款条件'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [<span>{row.paymentTerms + '-' + row.paymentTermsName}</span>]
            }
          }
        },
        {
          field: 'publishTime',
          title: this.$t('发布时间'),
          minWidth: 140
        },
        {
          field: 'feedbackTime',
          title: this.$t('反馈时间'),
          minWidth: 140
        },
        {
          field: 'syncErpStatus',
          title: this.$t('同步极光状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = syncOaStatusList.find((item) => item.value === row.syncErpStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'syncErpDesc',
          title: this.$t('同步极光接口返回'),
          minWidth: 150
        },
        {
          field: 'syncOaStatus',
          title: this.$t('同步OA状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = syncOaStatusList.find((item) => item.value === row.syncOaStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'syncOaDesc',
          title: this.$t('同步OA接口返回'),
          minWidth: 140
        },
        {
          field: 'purchaseRemark',
          title: this.$t('采方备注'),
          minWidth: 140
        },
        {
          field: 'supplierRemark',
          title: this.$t('供方备注'),
          minWidth: 140
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        },
        {
          field: 'updateUserName',
          title: this.$t('最后修改人'),
          minWidth: 130
        },
        {
          field: 'updateTime',
          title: this.$t('最后修改时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    if (!this.$route.path.includes('-sup')) {
      this.toolbar = listToolbar
    } else {
      this.isSup = true
      this.toolbar = [
        { code: 'edit', name: this.$t('编辑'), status: 'info' },
        { code: 'batchConfirmList', name: this.$t('确认'), status: 'info' },
        { code: 'export', name: this.$t('导出'), status: 'info' }
      ]
    }
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(e, field) {
      const startField = field + 'Start'
      const endField = field + 'End'
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      // 供方过滤草稿状态
      if (
        this.isSup &&
        !(this.searchFormModel.orderStatus && this.searchFormModel.orderStatus.length)
      ) {
        params.orderStatus = [1, 2, 3, 4, 5, 6, 7, 9]
      }
      this.loading = true
      const res = await this.$API.purchaseOrderMgt
        .queryList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!['add', 'edit', 'export'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      if (['checkOaList', 'edit'].includes(e.code)) {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅选择一条数据！'), type: 'warning' })
          return
        }
      }
      const ids = []
      for (let index = 0; index < selectedRecords.length; index++) {
        const item = selectedRecords[index]
        if (e.code === 'edit' && this.isSup && ![1].includes(item.orderStatus)) {
          this.$toast({ content: this.$t('仅支持编辑待供应商确认状态的数据！'), type: 'warning' })
          return
        }
        if (e.code === 'deleteList' && ![0].includes(item.orderStatus)) {
          this.$toast({ content: this.$t('仅支持删除草稿状态的数据！'), type: 'warning' })
          return
        }
        if (e.code === 'closeDetailList' && [0].includes(item.orderStatus)) {
          this.$toast({
            content: this.$t('仅支持关闭非草稿和完成状态的数据！'),
            type: 'warning'
          })
          return
        }
        if (e.code === 'publishList' && ![0, 2, 4].includes(item.orderStatus)) {
          this.$toast({
            content: this.$t('仅支持发布草稿、供应商已拒绝或采购已拒绝状态的数据！！'),
            type: 'warning'
          })
          return
        }
        if (e.code === 'batchConfirmList' && ![1].includes(item.orderStatus)) {
          this.$toast({
            content: this.$t('请选择待供应商确认的订单确认！！'),
            type: 'warning'
          })
          return
        }
        if (e.code === 'submitOaList' && ![3, 4, 5].includes(item.orderStatus)) {
          this.$toast({
            content: this.$t('仅支持供应商已确认状态的数据！！'),
            type: 'warning'
          })
          return
        }
        if (e.code === 'syncList' && ![5, 6, 7, 9].includes(item.orderStatus)) {
          // 双方已确认、已完成、已关闭、已确认状态的数据可以同步极光系统
          this.$toast({
            content: this.$t('仅支持双方已确认、已完成、已关闭、已确认状态的数据！！'),
            type: 'warning'
          })
          return
        }
        ids.push(item.id)
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'edit':
          this.handleAdd('edit', selectedRecords)
          break
        case 'deleteList': // 删除
          this.handleOperate(ids, e.code)
          break
        case 'publishList': // 发布
          this.handleOperate(ids, e.code)
          break
        case 'batchConfirmList': // 确认
          this.handleOperate(ids, e.code)
          break
        case 'confirmList': // 确认
          this.handleOperate(ids, e.code)
          break
        case 'submitOaList': // 提交
          this.handleOperate(ids, e.code)
          break
        case 'checkOaList': // 查看OA审批
          if (![1, 2, 3].includes(selectedRecords[0].approvalStatus)) {
            this.$toast({
              content: this.$t(
                '仅支持状态为审批中、审批通过和审批拒绝的单据跳转至OA系统查看审批！'
              ),
              type: 'warning'
            })
            return
          }
          this.handleCheckOa(ids)
          break
        case 'closeDetailList': // 关闭
          this.handleOperate(ids, e.code)
          break
        case 'syncList': // 同步
          this.handleOperate(ids, e.code)
          break
        case 'export':
          this.handleExport()
          break
        case 'downloadSealFile': // 下载用印文件
          this.handleDownloadSealFile(ids)
          break
        default:
          break
      }
    },
    handleCheckOa(ids) {
      this.$API.purchaseOrderMgt.checkOaList({ id: ids[0] }).then((res) => {
        if (res.code === 200) {
          window.open(res.data)
        } else {
          this.$toast({ content: res.message, type: 'warning' })
        }
      })
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.purchaseOrderMgt.exportList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'orderCode') {
        this.$router.push({
          name: this.isSup
            ? 'purchase-coordination-pv-detail-sup'
            : 'purchase-coordination-pv-detail',
          query: {
            type: 'detail',
            id: row.id,
            refreshId: Date.now()
          }
        })
      }
    },
    // 新增/编辑
    handleAdd(type, selectedRecords) {
      if (type === 'edit') {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅选择一条数据！'), type: 'warning' })
          return
        }
        if (
          !this.isSup &&
          !([0, 2, 4, 5].includes(selectedRecords[0].orderStatus) ||
            (selectedRecords[0].orderStatus === 3 && selectedRecords[0].approvalStatus === 0))
        ) {
          this.$toast({
            content: this.$t(
              '仅草稿、供应商已拒绝、采购已拒绝、双方已确认，或待采购确认且未审批的订单可以编辑'
            ),
            type: 'warning'
          })
          return
        }
        this.$router.push({
          name: this.isSup
            ? 'purchase-coordination-pv-detail-sup'
            : 'purchase-coordination-pv-detail',
          query: {
            type: 'edit',
            id: selectedRecords[0].id,
            refreshId: Date.now()
          }
        })
        return
      }
      this.$router.push({
        name: this.isSup
          ? 'purchase-coordination-pv-detail-sup'
          : 'purchase-coordination-pv-detail',
        query: {
          type: 'add',
          refreshId: Date.now()
        }
      })
    },
    // 删除、提交
    handleOperate(ids, type, extraParams = {}) {
      const tipMap = {
        deleteList: '删除',
        submitOaList: '提交',
        closeDetailList: '关闭',
        publishList: '发布',
        confirmList: '确认',
        syncList: '同步',
        batchConfirmList: '确认'
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确定${tipMap[type]}选中的数据？`)
        },
        success: async () => {
          const res = await this.$API.purchaseOrderMgt[type]({ ids, ...extraParams })
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${tipMap[type]}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    handleDownloadSealFile(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确定下载选中数据的用印文件？`)
        },
        success: () => {
          this.$API.purchaseOrderMgt.downloadSealFile({ ids }).then((res) => {
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
